* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Orbitron', monospace;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    overflow: hidden;
    height: 100vh;
}

#gameContainer {
    position: relative;
    width: 100vw;
    height: 100vh;
}

/* <PERSON><PERSON> */
.menu {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 10;
}

.menu.active {
    opacity: 1;
    visibility: visible;
}

.menu-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(30, 60, 114, 0.95) 0%, rgba(42, 82, 152, 0.95) 100%);
    backdrop-filter: blur(10px);
}

.menu-content {
    position: relative;
    text-align: center;
    color: white;
    z-index: 2;
}

.game-title {
    font-size: 4rem;
    font-weight: 900;
    margin-bottom: 2rem;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
    background: linear-gradient(45deg, #FFD700, #FFA500, #FF6347);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { text-shadow: 0 0 20px rgba(255, 215, 0, 0.5); }
    to { text-shadow: 0 0 30px rgba(255, 215, 0, 0.8), 0 0 40px rgba(255, 215, 0, 0.6); }
}

.menu-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 2rem;
    color: #FFD700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.menu-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
}

.menu-btn {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    border: none;
    color: white;
    padding: 15px 30px;
    font-size: 1.2rem;
    font-weight: 700;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 250px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    font-family: 'Orbitron', monospace;
}

.menu-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
    background: linear-gradient(45deg, #5CBF60, #4CAF50);
}

.menu-btn.secondary {
    background: linear-gradient(45deg, #757575, #616161);
}

.menu-btn.secondary:hover {
    background: linear-gradient(45deg, #858585, #757575);
}

.btn-icon {
    font-size: 1.5rem;
}

/* Takım Seçimi */
.team-selection {
    display: flex;
    align-items: center;
    gap: 3rem;
    margin: 2rem 0;
}

.team-selector h3 {
    color: #FFD700;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.team-options {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.team-option {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid transparent;
    border-radius: 10px;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    min-width: 120px;
}

.team-option:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.team-option.selected {
    border-color: #FFD700;
    background: rgba(255, 215, 0, 0.2);
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.5);
}

.team-flag {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.team-name {
    font-size: 0.9rem;
    font-weight: 600;
}

.vs-divider {
    font-size: 3rem;
    font-weight: 900;
    color: #FFD700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

/* Ayarlar */
.settings-options {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin: 2rem 0;
    text-align: left;
}

.setting-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem;
    border-radius: 10px;
}

.setting-item label {
    min-width: 150px;
    font-weight: 600;
}

.setting-item input[type="range"] {
    flex: 1;
    margin: 0 1rem;
}

.setting-item select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.5rem;
    border-radius: 5px;
    font-family: 'Orbitron', monospace;
}

/* Oyun Ekranı */
.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: none;
}

.screen.active {
    display: block;
}

#gameUI {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 5;
    padding: 20px;
}

.score-board {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    background: rgba(0, 0, 0, 0.7);
    padding: 1rem 2rem;
    border-radius: 15px;
    margin: 0 auto;
    width: fit-content;
    backdrop-filter: blur(10px);
}

.team-score {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: white;
    font-weight: 700;
    font-size: 1.5rem;
}

.time-display {
    color: #FFD700;
    font-size: 2rem;
    font-weight: 900;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.pause-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.7);
    border: none;
    color: white;
    font-size: 1.5rem;
    padding: 10px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.pause-btn:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: scale(1.1);
}

#gameCanvas {
    display: block;
    margin: 0 auto;
    background: #2E8B57;
    border: 3px solid #fff;
    border-radius: 10px;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.5);
}

/* Responsive */
@media (max-width: 768px) {
    .game-title {
        font-size: 2.5rem;
    }

    .team-selection {
        flex-direction: column;
        gap: 2rem;
    }

    .vs-divider {
        font-size: 2rem;
    }

    .menu-btn {
        min-width: 200px;
        font-size: 1rem;
    }
}

/* İstatistik Paneli */
.stats-panel {
    position: absolute;
    top: 80px;
    right: 20px;
    width: 280px;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 10px;
    padding: 15px;
    color: white;
    font-size: 12px;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    z-index: 5;
}

.stats-panel.active {
    transform: translateX(0);
}

.stats-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    padding-bottom: 10px;
}

.stats-header h3 {
    font-size: 16px;
    color: #FFD700;
}

.toggle-stats {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 5px;
    border-radius: 5px;
    transition: background 0.3s ease;
}

.toggle-stats:hover {
    background: rgba(255, 255, 255, 0.2);
}

.player-stats {
    margin-bottom: 15px;
}

.player-stats h4 {
    color: #4CAF50;
    margin-bottom: 8px;
    font-size: 14px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    padding: 2px 0;
}

.stat-item span:first-child {
    color: #ccc;
}

.stat-item span:last-child {
    color: #FFD700;
    font-weight: bold;
}

/* Gelişmiş Takım Seçenekleri */
.team-options {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    max-height: 200px;
    overflow-y: auto;
    padding: 10px;
}

.team-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px 10px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid transparent;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 80px;
}

.team-option:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.team-option.selected {
    border-color: #FFD700;
    background: rgba(255, 215, 0, 0.2);
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
}

.team-flag {
    font-size: 24px;
    margin-bottom: 8px;
}

.team-name {
    font-size: 12px;
    text-align: center;
    font-weight: bold;
}

/* Ses Efektleri Göstergesi */
.sound-indicator {
    position: absolute;
    top: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 6;
}

.sound-indicator.show {
    opacity: 1;
}

/* Animasyonlar */
@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes slideIn {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes glow {
    0%, 100% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.5); }
    50% { box-shadow: 0 0 30px rgba(255, 215, 0, 0.8); }
}

@keyframes goalCelebration {
    0% { transform: scale(1) rotate(0deg); }
    25% { transform: scale(1.2) rotate(5deg); }
    50% { transform: scale(1.1) rotate(-5deg); }
    75% { transform: scale(1.2) rotate(3deg); }
    100% { transform: scale(1) rotate(0deg); }
}
