#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Firefox Tamamen Sıfırlama Aracı
Bu script Firefox'u tamamen sıfırlar ve tüm kullanıcı verilerini siler.
UYARI: Bu işlem geri alınamaz!
"""

import os
import sys
import shutil
import subprocess
import time
import platform
from pathlib import Path
import psutil

class FirefoxResetter:
    def __init__(self):
        self.system = platform.system()
        self.firefox_profiles_path = self._get_firefox_profiles_path()
        
    def _get_firefox_profiles_path(self):
        """İşletim sistemine göre Firefox profil klasörünü bulur"""
        if self.system == "Windows":
            return Path(os.environ['APPDATA']) / "Mozilla" / "Firefox" / "Profiles"
        elif self.system == "Darwin":  # macOS
            return Path.home() / "Library" / "Application Support" / "Firefox" / "Profiles"
        else:  # Linux
            return Path.home() / ".mozilla" / "firefox"
    
    def _is_firefox_running(self):
        """Firefox'un çalışıp çalışmadığını kontrol eder"""
        firefox_processes = []
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if 'firefox' in proc.info['name'].lower():
                    firefox_processes.append(proc)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return firefox_processes
    
    def _kill_firefox_processes(self):
        """Tüm Firefox işlemlerini sonlandırır"""
        print("🔄 Firefox işlemleri sonlandırılıyor...")
        firefox_processes = self._is_firefox_running()
        
        if not firefox_processes:
            print("✅ Firefox zaten kapalı.")
            return True
        
        # Önce nazikçe kapatmayı dene
        for proc in firefox_processes:
            try:
                proc.terminate()
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        # 5 saniye bekle
        time.sleep(5)
        
        # Hala çalışan işlemler varsa zorla kapat
        remaining_processes = self._is_firefox_running()
        for proc in remaining_processes:
            try:
                proc.kill()
                print(f"🔥 Firefox işlemi zorla sonlandırıldı (PID: {proc.pid})")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        # Son kontrol
        time.sleep(2)
        if self._is_firefox_running():
            print("❌ Bazı Firefox işlemleri hala çalışıyor. Manuel olarak kapatın.")
            return False
        
        print("✅ Tüm Firefox işlemleri sonlandırıldı.")
        return True
    
    def _backup_bookmarks(self):
        """Yer imlerini yedekler (isteğe bağlı)"""
        backup_dir = Path.cwd() / "firefox_backup"
        backup_dir.mkdir(exist_ok=True)
        
        if not self.firefox_profiles_path.exists():
            return
        
        for profile_dir in self.firefox_profiles_path.iterdir():
            if profile_dir.is_dir():
                places_db = profile_dir / "places.sqlite"
                if places_db.exists():
                    backup_file = backup_dir / f"bookmarks_{profile_dir.name}_{int(time.time())}.sqlite"
                    shutil.copy2(places_db, backup_file)
                    print(f"📚 Yer imleri yedeklendi: {backup_file}")
    
    def _delete_profiles(self):
        """Tüm Firefox profillerini siler"""
        if not self.firefox_profiles_path.exists():
            print("❌ Firefox profil klasörü bulunamadı.")
            return False
        
        print(f"🗂️ Profil klasörü: {self.firefox_profiles_path}")
        
        try:
            # Profil klasörünün içindeki tüm klasörleri sil
            for item in self.firefox_profiles_path.iterdir():
                if item.is_dir():
                    print(f"🗑️ Profil siliniyor: {item.name}")
                    shutil.rmtree(item)
                elif item.is_file():
                    print(f"🗑️ Dosya siliniyor: {item.name}")
                    item.unlink()
            
            print("✅ Tüm Firefox profilleri silindi.")
            return True
            
        except Exception as e:
            print(f"❌ Profil silme hatası: {e}")
            return False
    
    def _delete_firefox_cache(self):
        """Firefox önbelleğini siler"""
        cache_paths = []
        
        if self.system == "Windows":
            cache_paths = [
                Path(os.environ['LOCALAPPDATA']) / "Mozilla" / "Firefox" / "Profiles",
                Path(os.environ['TEMP']) / "mozilla-temp-files"
            ]
        elif self.system == "Darwin":
            cache_paths = [
                Path.home() / "Library" / "Caches" / "Firefox"
            ]
        else:  # Linux
            cache_paths = [
                Path.home() / ".cache" / "mozilla" / "firefox"
            ]
        
        for cache_path in cache_paths:
            if cache_path.exists():
                try:
                    shutil.rmtree(cache_path)
                    print(f"🧹 Önbellek silindi: {cache_path}")
                except Exception as e:
                    print(f"⚠️ Önbellek silme hatası: {e}")
    
    def _start_firefox(self):
        """Firefox'u yeniden başlatır"""
        try:
            if self.system == "Windows":
                subprocess.Popen(["firefox"])
            elif self.system == "Darwin":
                subprocess.Popen(["open", "-a", "Firefox"])
            else:
                subprocess.Popen(["firefox"])
            
            print("🚀 Firefox yeniden başlatıldı.")
            return True
        except Exception as e:
            print(f"⚠️ Firefox başlatma hatası: {e}")
            print("Firefox'u manuel olarak başlatabilirsiniz.")
            return False
    
    def reset_firefox(self, backup_bookmarks=True, restart_firefox=True):
        """Firefox'u tamamen sıfırlar"""
        print("🔥 Firefox Tamamen Sıfırlama İşlemi Başlıyor...")
        print("=" * 50)
        
        # 1. Firefox'u kapat
        if not self._kill_firefox_processes():
            print("❌ Firefox kapatılamadı. İşlem durduruluyor.")
            return False
        
        # 2. Yer imlerini yedekle (isteğe bağlı)
        if backup_bookmarks:
            print("\n📚 Yer imleri yedekleniyor...")
            self._backup_bookmarks()
        
        # 3. Profilleri sil
        print("\n🗑️ Firefox profilleri siliniyor...")
        if not self._delete_profiles():
            return False
        
        # 4. Önbelleği temizle
        print("\n🧹 Firefox önbelleği temizleniyor...")
        self._delete_firefox_cache()
        
        # 5. Firefox'u yeniden başlat
        if restart_firefox:
            print("\n🚀 Firefox yeniden başlatılıyor...")
            time.sleep(2)
            self._start_firefox()
        
        print("\n" + "=" * 50)
        print("✅ Firefox tamamen sıfırlandı!")
        print("🎉 Firefox artık fabrika ayarlarında.")
        
        if backup_bookmarks:
            print("📚 Yer imleri 'firefox_backup' klasöründe yedeklendi.")
        
        return True

def main():
    print("🦊 Firefox Tamamen Sıfırlama Aracı")
    print("=" * 40)
    print("⚠️  UYARI: Bu işlem TÜM Firefox verilerinizi silecek!")
    print("   - Yer imleri")
    print("   - Şifreler") 
    print("   - Geçmiş")
    print("   - Eklentiler")
    print("   - Ayarlar")
    print("=" * 40)
    
    # Kullanıcı onayı
    response = input("\n❓ Devam etmek istediğinizden emin misiniz? (EVET/hayır): ")
    if response.upper() != "EVET":
        print("❌ İşlem iptal edildi.")
        return
    
    # Yer imi yedekleme seçeneği
    backup_choice = input("\n📚 Yer imlerini yedeklemek istiyor musunuz? (E/h): ")
    backup_bookmarks = backup_choice.upper() in ['E', 'EVET', 'Y', 'YES']
    
    # Firefox'u yeniden başlatma seçeneği
    restart_choice = input("\n🚀 İşlem sonrası Firefox'u başlatmak istiyor musunuz? (E/h): ")
    restart_firefox = restart_choice.upper() in ['E', 'EVET', 'Y', 'YES']
    
    # Sıfırlama işlemini başlat
    resetter = FirefoxResetter()
    success = resetter.reset_firefox(backup_bookmarks, restart_firefox)
    
    if success:
        print("\n🎊 İşlem başarıyla tamamlandı!")
    else:
        print("\n💥 İşlem sırasında hata oluştu!")
    
    input("\nÇıkmak için Enter'a basın...")

if __name__ == "__main__":
    main()
